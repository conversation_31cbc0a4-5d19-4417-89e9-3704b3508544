import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'theme/app_theme.dart';
import 'screens/auth_wrapper.dart';
import 'services/translation_service.dart';
import 'services/api_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase first (required for auth)
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  // Load environment variables (needed for API service)
  await dotenv.load(fileName: ".env");

  // Start app immediately, defer other initialization
  runApp(const LingoFlowApp());

  // Initialize other services in background after app starts
  _initializeServicesInBackground();
}

void _initializeServicesInBackground() async {
  try {
    // Initialize API service (dotenv already loaded)
    ApiService().initialize();

    // Load language preferences
    await TranslationService().loadLanguagePreferences();
  } catch (e) {
    debugPrint('Background initialization error: $e');
  }
}

class LingoFlowApp extends StatelessWidget {
  const LingoFlowApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'LingoFlow',
      debugShowCheckedModeBanner: false,
      theme: AppTheme.lightTheme,
      home: const AuthWrapper(),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:iconsax/iconsax.dart';
import '../services/translation_service.dart';

class Difficulty {
  final String id;
  final String nameKey;
  final IconData icon;
  final Color primaryColor;
  final Color secondaryColor;

  const Difficulty({
    required this.id,
    required this.nameKey,
    required this.icon,
    required this.primaryColor,
    required this.secondaryColor,
  });

  String get name => TranslationService().translate(nameKey);
}

class Difficulties {
  static const List<Difficulty> all = [
    Difficulty(
      id: 'easy',
      nameKey: 'easy',
      icon: Iconsax.star, // Kolay seviye
      primaryColor: Color(0xFF370789),
      secondaryColor: Color(0xFFB6FF71),
    ),
    Difficulty(
      id: 'intermediate',
      nameKey: 'intermediate',
      icon: Iconsax.medal, // Orta seviye
      primaryColor: Color(0xFF370789),
      secondaryColor: Color(0xFFB6FF71),
    ),
    Difficulty(
      id: 'advanced',
      nameKey: 'advanced',
      icon: Iconsax.crown, // İleri seviye
      primaryColor: Color(0xFF370789),
      secondaryColor: Color(0xFFB6FF71),
    ),
  ];

  static Difficulty getById(String id) {
    return all.firstWhere((difficulty) => difficulty.id == id);
  }
}

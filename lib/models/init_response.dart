class InitResponse {
  final List<LocalizationResponse> localizationResponses;
  final List<Language> languages;
  final List<CategoryData> categories;
  final List<TenseCategoryData> tenseCategories;

  InitResponse({
    required this.localizationResponses,
    required this.languages,
    required this.categories,
    required this.tenseCategories,
  });

  factory InitResponse.fromJson(Map<String, dynamic> json) {
    return InitResponse(
      localizationResponses: (json['localizationResponses'] as List)
          .map((e) => LocalizationResponse.fromJson(e))
          .toList(),
      languages: (json['languages'] as List)
          .map((e) => Language.fromJson(e))
          .toList(),
      categories: (json['categories'] as List)
          .map((e) => CategoryData.fromJson(e))
          .toList(),
      tenseCategories: (json['tenseCategories'] as List)
          .map((e) => TenseCategoryData.fromJson(e))
          .toList(),
    );
  }
}

class LocalizationResponse {
  final String language;
  final String languageCode;
  final Map<String, String> data;

  LocalizationResponse({
    required this.language,
    required this.languageCode,
    required this.data,
  });

  factory LocalizationResponse.fromJson(Map<String, dynamic> json) {
    return LocalizationResponse(
      language: json['language'] as String,
      languageCode: json['language_code'] as String,
      data: Map<String, String>.from(json['data'] as Map),
    );
  }
}

class Language {
  final String code;
  final String name;
  final String icon;

  Language({required this.code, required this.name, required this.icon});

  factory Language.fromJson(Map<String, dynamic> json) {
    return Language(
      code: json['code'] as String,
      name: json['name'] as String,
      icon: json['icon'] as String,
    );
  }
}

class CategoryData {
  final String id;
  final String nameKey;

  CategoryData({required this.id, required this.nameKey});

  factory CategoryData.fromJson(Map<String, dynamic> json) {
    return CategoryData(
      id: json['id'] as String,
      nameKey: json['nameKey'] as String,
    );
  }
}

class TenseCategoryData {
  final int id;
  final String nameKey;

  TenseCategoryData({required this.id, required this.nameKey});

  factory TenseCategoryData.fromJson(Map<String, dynamic> json) {
    return TenseCategoryData(
      id: json['id'] as int,
      nameKey: json['nameKey'] as String,
    );
  }
}

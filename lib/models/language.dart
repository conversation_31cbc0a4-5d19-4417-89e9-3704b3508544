class Language {
  final String code;
  final String name;
  final String icon;

  const Language({required this.code, required this.name, required this.icon});
}

class Languages {
  static const List<Language> all = [
    Language(code: 'english-en', name: 'English', icon: '🇬🇧'),
    Language(code: 'turkish-tr', name: 'Turkish', icon: '🇹🇷'),
    Language(code: 'spanish-es', name: 'Spanish', icon: '🇪🇸'),
    Language(code: 'french-fr', name: 'French', icon: '🇫🇷'),
    Language(code: 'german-de', name: 'German', icon: '🇩🇪'),
    Language(code: 'italian-it', name: 'Italian', icon: '🇮🇹'),
    Language(code: 'japanese-ja', name: 'Japanese', icon: '🇯🇵'),
    Language(code: 'korean-ko', name: 'Korean', icon: '🇰🇷'),
    Language(code: 'portuguese-pt', name: 'Portuguese', icon: '🇧🇷'),
    Language(code: 'dutch-nl', name: 'Dutch', icon: '🇳🇱'),
    Language(code: 'hindi-hi', name: 'Hindi', icon: '🇮🇳'),
  ];

  static Language getByCode(String code) {
    return all.firstWhere((language) => language.code == code);
  }
}

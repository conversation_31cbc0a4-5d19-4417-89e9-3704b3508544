import 'package:flutter/material.dart';
import 'package:iconsax/iconsax.dart';
import '../services/translation_service.dart';

class Category {
  final String id;
  final String nameKey;
  final IconData icon;
  final Color primaryColor;
  final Color secondaryColor;

  const Category({
    required this.id,
    required this.name<PERSON><PERSON>,
    required this.icon,
    required this.primaryColor,
    required this.secondaryColor,
  });

  String get name => TranslationService().translate(nameKey);
}

class Categories {
  static const List<Category> all = [
    Category(
      id: 'everyday',
      nameKey: 'everyday',
      icon: Iconsax.home_2,
      primaryColor: Color(0xFF370789),
      secondaryColor: Color(0xFFB6FF71),
    ),
    Category(
      id: 'business',
      nameKey: 'business',
      icon: Iconsax.briefcase,
      primaryColor: Color(0xFF370789),
      secondaryColor: Color(0xFFB6FF71),
    ),
    Category(
      id: 'travel',
      nameKey: 'travel',
      icon: Iconsax.airplane,
      primaryColor: Color(0xFF370789),
      secondaryColor: Color(0xFFB6FF71),
    ),
    Category(
      id: 'academic',
      nameKey: 'academic',
      icon: Iconsax.book_1,
      primaryColor: Color(0xFF370789),
      secondaryColor: Color(0xFFB6FF71),
    ),
  ];

  static Category getById(String id) {
    return all.firstWhere((category) => category.id == id);
  }
}

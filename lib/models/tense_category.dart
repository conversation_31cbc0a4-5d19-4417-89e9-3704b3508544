import 'package:flutter/material.dart';
import 'package:iconsax/iconsax.dart';

class TenseCategory {
  final int id;
  final String nameKey;

  const TenseCategory({required this.id, required this.nameKey});

  // Factory constructor for API data
  factory TenseCategory.fromApiData(int id, String nameKey) {
    return TenseCategory(id: id, nameKey: nameKey);
  }

  // Legacy enum-like values for backward compatibility
  static const TenseCategory present = TenseCategory(id: 0, nameKey: 'present');
  static const TenseCategory past = TenseCategory(id: 1, nameKey: 'past');
  static const TenseCategory future = TenseCategory(id: 2, nameKey: 'future');
  static const TenseCategory perfect = TenseCategory(id: 3, nameKey: 'perfect');

  static const List<TenseCategory> values = [present, past, future, perfect];

  static TenseCategory fromValue(int value) {
    return values.firstWhere((e) => e.id == value, orElse: () => present);
  }

  static TenseCategory getTenseById(int id) {
    return values.firstWhere((e) => e.id == id, orElse: () => present);
  }

  IconData get icon {
    switch (nameKey.toLowerCase()) {
      case 'present':
        return Iconsax.clock; // Present time
      case 'past':
        return Iconsax.backward; // Past time
      case 'future':
        return Iconsax.forward; // Future time
      case 'perfect':
        return Iconsax.crown; // Perfect tense
      default:
        return Iconsax.clock; // Default icon
    }
  }

  Color get primaryColor {
    return const Color(0xFF370789); // Same color for all
  }

  Color get secondaryColor {
    return const Color(0xFFB6FF71); // Same color for all
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TenseCategory && other.id == id && other.nameKey == nameKey;
  }

  @override
  int get hashCode => Object.hash(id, nameKey);
}

import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'auth_screen.dart';
import 'main_screen.dart';
import '../services/initialization_service.dart';

class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  final InitializationService _initializationService = InitializationService();

  @override
  void initState() {
    super.initState();
    // Initialize data in background after auth check
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeDataInBackground();
    });
  }

  Future<void> _initializeDataInBackground() async {
    try {
      // Initialize app data in background without blocking UI
      await _initializationService.initialize();
    } catch (e) {
      // Silently handle initialization errors - app can still work with fallback data
      print('Background initialization failed: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<User?>(
      stream: FirebaseAuth.instance.authStateChanges(),
      builder: (context, snapshot) {
        // Show loading while checking auth state
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        }

        // Show appropriate screen based on auth state
        if (snapshot.hasData) {
          return const MainScreen();
        } else {
          return const AuthScreen();
        }
      },
    );
  }
}

import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import '../models/language.dart';
import '../widgets/language_selection_modal.dart';
import '../services/translation_service.dart';
import '../services/auth_service.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  String _nativeLanguageCode = 'turkish-tr'; // Default anadil
  String _targetLanguageCode = 'english-en'; // Default hedef dil
  final TranslationService _translationService = TranslationService();
  final AuthService _authService = AuthService();

  @override
  void initState() {
    super.initState();
    _loadLanguageSettings();

    // Listen to auth changes
    _authService.addListener(_onAuthStateChanged);
    _translationService.addListener(_onLanguageChanged);
  }

  @override
  void dispose() {
    _authService.removeListener(_onAuthStateChanged);
    _translationService.removeListener(_onLanguageChanged);
    super.dispose();
  }

  void _onAuthStateChanged() {
    if (mounted) {
      // Only rebuild if we're currently visible
      ModalRoute.of(context)?.isCurrent == true
          ? setState(() {
              // Refresh UI when auth state changes
            })
          : null;
    }
  }

  void _onLanguageChanged() {
    if (mounted) {
      // Only rebuild if we're currently visible to avoid unnecessary work
      ModalRoute.of(context)?.isCurrent == true
          ? setState(() {
              // Refresh UI when language changes - only text, not data
            })
          : null;
    }
  }

  Future<void> _loadLanguageSettings() async {
    await _translationService.loadLanguagePreferences();
    setState(() {
      _nativeLanguageCode = _translationService.nativeLanguageCode;
      _targetLanguageCode = _translationService.targetLanguageCode;
    });
  }

  Future<void> _updateLanguageSettings(
    String nativeCode,
    String targetCode,
  ) async {
    await _translationService.saveLanguagePreferences(nativeCode, targetCode);
    setState(() {
      _nativeLanguageCode = nativeCode;
      _targetLanguageCode = targetCode;
    });
  }

  Future<void> _handleSignOut() async {
    try {
      // Show confirmation dialog
      final bool? shouldSignOut = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Sign Out'),
          content: const Text('Are you sure you want to sign out?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: const Text('Sign Out'),
            ),
          ],
        ),
      );

      if (shouldSignOut == true) {
        await _authService.signOut();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to sign out: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final nativeLanguage = Languages.getByCode(_nativeLanguageCode);
    final targetLanguage = Languages.getByCode(_targetLanguageCode);

    return Scaffold(
      appBar: AppBar(
        title: Text(_translationService.translate('profile')),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Profile header
            _buildProfileHeader(),

            const SizedBox(height: 32),

            // Language Settings Section
            _buildSectionTitle(
              _translationService.translate('language_settings'),
            ),
            const SizedBox(height: 16),

            // Native Language
            _buildLanguageSelector(
              title: _translationService.translate('native_language'),
              description: _translationService.translate(
                'native_language_desc',
              ),
              language: nativeLanguage,
              onTap: () => _showLanguageSelection(
                context: context,
                title: _translationService.translate('select_native_language'),
                description: _translationService.translate(
                  'select_native_language_desc',
                ),
                selectedCode: _nativeLanguageCode,
                excludeCode: _targetLanguageCode,
                onSelected: (language) {
                  _updateLanguageSettings(language.code, _targetLanguageCode);
                },
              ),
            ),

            const SizedBox(height: 16),

            // Target Language
            _buildLanguageSelector(
              title: _translationService.translate('learning_language'),
              description: _translationService.translate(
                'learning_language_desc',
              ),
              language: targetLanguage,
              onTap: () => _showLanguageSelection(
                context: context,
                title: _translationService.translate(
                  'select_learning_language',
                ),
                description: _translationService.translate(
                  'select_learning_language_desc',
                ),
                selectedCode: _targetLanguageCode,
                excludeCode: _nativeLanguageCode,
                onSelected: (language) {
                  _updateLanguageSettings(_nativeLanguageCode, language.code);
                },
              ),
            ),

            const SizedBox(height: 20),

            // Language Swap Button
            _buildLanguageSwapButton(),

            const SizedBox(height: 32),

            // Account Section
            _buildSectionTitle(_translationService.translate('account')),
            const SizedBox(height: 16),
            _buildMenuTile(
              icon: Icons.person_outline,
              title: _translationService.translate('my_account'),
              subtitle: _translationService.translate('my_account_desc'),
              onTap: () {},
            ),

            const SizedBox(height: 16),

            // Subscription Section
            _buildSectionTitle(_translationService.translate('subscription')),
            const SizedBox(height: 16),
            _buildMenuTile(
              icon: Icons.star_outline,
              title: _translationService.translate('my_subscription'),
              subtitle: _translationService.translate('my_subscription_desc'),
              onTap: () {},
            ),

            const SizedBox(height: 16),

            // Options Section
            _buildSectionTitle(_translationService.translate('options')),
            const SizedBox(height: 16),
            _buildMenuTile(
              icon: Icons.settings_brightness_outlined,
              title: _translationService.translate('settings'),
              subtitle: _translationService.translate('settings_desc'),
              onTap: () {},
            ),

            const SizedBox(height: 16),

            // Sign Out Button
            _buildMenuTile(
              icon: Icons.logout,
              title: 'Sign Out',
              subtitle: 'Sign out from your account',
              onTap: _handleSignOut,
              isDestructive: true,
            ),

            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: AppColors.surface, width: 1),
        boxShadow: [
          BoxShadow(
            color: AppColors.surface.withOpacity(0.5),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          // Avatar
          Container(
            width: 64,
            height: 64,
            decoration: BoxDecoration(
              color: AppColors.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(32),
            ),
            child: _authService.photoURL != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(32),
                    child: Image.network(
                      _authService.photoURL!,
                      width: 64,
                      height: 64,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => const Icon(
                        Icons.person,
                        color: AppColors.primary,
                        size: 32,
                      ),
                    ),
                  )
                : const Icon(Icons.person, color: AppColors.primary, size: 32),
          ),

          const SizedBox(width: 20),

          // User info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${_translationService.translate('welcome_back')}, ${_authService.displayName}!',
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _authService.email.isNotEmpty
                      ? _authService.email
                      : _translationService.translate('keep_practicing'),
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: AppColors.textPrimary,
      ),
    );
  }

  Widget _buildLanguageSelector({
    required String title,
    required String description,
    required Language language,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: AppColors.background,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: AppColors.surface, width: 1),
          boxShadow: [
            BoxShadow(
              color: AppColors.surface.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // Language flag
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: AppColors.surface.withOpacity(0.5),
                borderRadius: BorderRadius.circular(24),
              ),
              child: Center(
                child: Text(
                  language.icon,
                  style: const TextStyle(fontSize: 24),
                ),
              ),
            ),

            const SizedBox(width: 16),

            // Language info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppColors.textSecondary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    language.name,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),

            // Arrow icon
            const Icon(
              Icons.arrow_forward_ios,
              color: AppColors.textSecondary,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMenuTile({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    final iconColor = isDestructive ? Colors.red : AppColors.textSecondary;
    final titleColor = isDestructive ? Colors.red : AppColors.textPrimary;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: AppColors.background,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: AppColors.surface, width: 1),
          boxShadow: [
            BoxShadow(
              color: AppColors.surface.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // Icon
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: isDestructive
                    ? Colors.red.withOpacity(0.1)
                    : AppColors.surface.withOpacity(0.5),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(icon, color: iconColor, size: 24),
            ),

            const SizedBox(width: 16),

            // Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: titleColor,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ),

            // Arrow icon
            Icon(
              isDestructive ? Icons.logout : Icons.arrow_forward_ios,
              color: iconColor,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  void _showLanguageSelection({
    required BuildContext context,
    required String title,
    required String description,
    required String selectedCode,
    required String excludeCode,
    required Function(Language) onSelected,
  }) {
    LanguageSelectionModal.show(
      context,
      selectedLanguageCode: selectedCode,
      excludeLanguageCode: excludeCode,
      onLanguageSelected: onSelected,
      title: title,
      description: description,
    );
  }

  Widget _buildLanguageSwapButton() {
    final nativeLanguage = Languages.getByCode(_nativeLanguageCode);
    final targetLanguage = Languages.getByCode(_targetLanguageCode);

    return Container(
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.surface, width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _translationService.translate('quick_swap'),
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _translationService.translate('swap_languages_desc'),
              style: const TextStyle(
                fontSize: 12,
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 16),

            // Swap UI
            Row(
              children: [
                // Native language
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: AppColors.surface.withOpacity(0.5),
                      ),
                    ),
                    child: Column(
                      children: [
                        Text(
                          nativeLanguage.icon,
                          style: const TextStyle(fontSize: 24),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          nativeLanguage.name,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: AppColors.textPrimary,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          _translationService.translate('native'),
                          style: const TextStyle(
                            fontSize: 10,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(width: 16),

                // Swap button
                GestureDetector(
                  onTap: _swapLanguages,
                  child: Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: AppColors.primary,
                      borderRadius: BorderRadius.circular(24),
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.primary.withOpacity(0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.swap_horiz,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ),

                const SizedBox(width: 16),

                // Target language
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: AppColors.surface.withOpacity(0.5),
                      ),
                    ),
                    child: Column(
                      children: [
                        Text(
                          targetLanguage.icon,
                          style: const TextStyle(fontSize: 24),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          targetLanguage.name,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: AppColors.textPrimary,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          _translationService.translate('learning'),
                          style: const TextStyle(
                            fontSize: 10,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _swapLanguages() {
    // Swap the languages
    final String tempNative = _nativeLanguageCode;
    final String tempTarget = _targetLanguageCode;

    _updateLanguageSettings(tempTarget, tempNative);

    // Show a brief feedback
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(_translationService.translate('languages_swapped')),
        duration: const Duration(seconds: 2),
        backgroundColor: AppColors.primary,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }
}

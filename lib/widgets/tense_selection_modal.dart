import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import '../models/tense_category.dart';
import '../services/translation_service.dart';
import '../services/initialization_service.dart';

class TenseSelectionModal extends StatefulWidget {
  final List<TenseCategory> selectedTenses;
  final Function(List<TenseCategory>) onTensesSelected;

  const TenseSelectionModal({
    super.key,
    required this.selectedTenses,
    required this.onTensesSelected,
  });

  @override
  State<TenseSelectionModal> createState() => _TenseSelectionModalState();
}

class _TenseSelectionModalState extends State<TenseSelectionModal> {
  late List<TenseCategory> _selectedTenses;
  final TranslationService _translationService = TranslationService();
  final InitializationService _initializationService = InitializationService();

  @override
  void initState() {
    super.initState();
    _selectedTenses = List.from(widget.selectedTenses);
  }

  @override
  Widget build(BuildContext context) {
    // Use dynamic tense categories from initialization service, fallback to static if not loaded
    final tenseCategories = _initializationService.isInitialized
        ? _initializationService.tenseCategories
        : TenseCategory.values;
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      decoration: const BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 12, bottom: 8),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: AppColors.surface,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.fromLTRB(24, 16, 24, 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        _translationService.translate('select_tenses'),
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary,
                        ),
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        setState(() {
                          _selectedTenses.clear();
                        });
                      },
                      child: Text(
                        _translationService.translate('clear_all'),
                        style: const TextStyle(
                          color: AppColors.primary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(
                        Icons.close,
                        color: AppColors.textSecondary,
                      ),
                      splashColor: Colors.transparent,
                      highlightColor: Colors.transparent,
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  _translationService.translate('select_tenses_desc'),
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),

          // Tense list
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.fromLTRB(24, 8, 24, 32),
              itemCount: tenseCategories.length,
              itemBuilder: (context, index) {
                final tense = tenseCategories[index];
                final isSelected = _selectedTenses.contains(tense);

                return Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () {
                        setState(() {
                          if (isSelected) {
                            _selectedTenses.remove(tense);
                          } else {
                            _selectedTenses.add(tense);
                          }
                        });
                      },
                      borderRadius: BorderRadius.circular(16),
                      child: Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: isSelected
                              ? tense.primaryColor.withOpacity(0.05)
                              : AppColors.background,
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: isSelected
                                ? tense.primaryColor
                                : AppColors.surface,
                            width: isSelected ? 2 : 1,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.surface.withOpacity(0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Row(
                          children: [
                            // Tense icon
                            Container(
                              width: 48,
                              height: 48,
                              decoration: BoxDecoration(
                                color: tense.secondaryColor.withOpacity(0.5),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Icon(
                                tense.icon,
                                color: tense.primaryColor,
                                size: 24,
                              ),
                            ),

                            const SizedBox(width: 16),

                            // Tense info
                            Expanded(
                              child: Text(
                                _translationService.translate(tense.nameKey),
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: isSelected
                                      ? AppColors.primary
                                      : AppColors.textPrimary,
                                ),
                              ),
                            ),

                            // Selection indicator
                            if (isSelected)
                              Icon(
                                Icons.check_circle,
                                color: tense.primaryColor,
                                size: 24,
                              ),
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),

          // Action buttons
          Padding(
            padding: const EdgeInsets.fromLTRB(24, 16, 24, 32),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  widget.onTensesSelected(_selectedTenses);
                  Navigator.of(context).pop();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  elevation: 0,
                ),
                child: Text(
                  '${_translationService.translate('apply')} (${_selectedTenses.length})',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

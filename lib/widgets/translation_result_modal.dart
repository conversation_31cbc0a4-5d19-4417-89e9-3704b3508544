import 'package:flutter/material.dart';
import 'package:confetti/confetti.dart';
import '../theme/app_theme.dart';

class TranslationResultModal extends StatefulWidget {
  final String userTranslation;
  final String suggestedTranslation;
  final String explanation;
  final int score;

  const TranslationResultModal({
    super.key,
    required this.userTranslation,
    required this.suggestedTranslation,
    required this.explanation,
    required this.score,
  });

  static Future<void> show(
    BuildContext context, {
    required String userTranslation,
    required String suggestedTranslation,
    required String explanation,
    required int score,
  }) {
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => TranslationResultModal(
        userTranslation: userTranslation,
        suggestedTranslation: suggestedTranslation,
        explanation: explanation,
        score: score,
      ),
    );
  }

  @override
  State<TranslationResultModal> createState() => _TranslationResultModalState();
}

class _TranslationResultModalState extends State<TranslationResultModal> {
  late ConfettiController _confettiController;

  @override
  void initState() {
    super.initState();
    _confettiController = ConfettiController(
      duration: const Duration(milliseconds: 200),
    );

    // Trigger confetti if score is 8 or higher - subtle celebration
    if (widget.score >= 8) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _confettiController.play();
      });
    }
  }

  @override
  void dispose() {
    _confettiController.dispose();
    super.dispose();
  }

  Color _getScoreColor(int score) {
    if (score >= 8) return Colors.green;
    if (score >= 5) return Colors.orange;
    return Colors.red;
  }

  String _getScoreLabel(int score) {
    if (score >= 8) return 'Excellent';
    if (score >= 5) return 'Good';
    return 'Needs Improvement';
  }

  IconData _getScoreIcon(int score) {
    if (score >= 8) return Icons.star;
    if (score >= 5) return Icons.thumb_up;
    return Icons.lightbulb_outline;
  }

  @override
  Widget build(BuildContext context) {
    final scoreColor = _getScoreColor(widget.score);

    return Stack(
      children: [
        Container(
          decoration: const BoxDecoration(
            color: AppColors.background,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(24),
              topRight: Radius.circular(24),
            ),
          ),
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.85,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Handle bar
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: AppColors.surface,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Header with score
              Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  children: [
                    // Score circle
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: scoreColor.withOpacity(0.1),
                        shape: BoxShape.circle,
                        border: Border.all(color: scoreColor, width: 3),
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            _getScoreIcon(widget.score),
                            color: scoreColor,
                            size: 24,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            '${widget.score}',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: scoreColor,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Score label
                    Text(
                      _getScoreLabel(widget.score),
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: scoreColor,
                      ),
                    ),

                    const SizedBox(height: 8),

                    const Text(
                      'Translation Result',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textPrimary,
                      ),
                    ),
                  ],
                ),
              ),

              // Content
              Flexible(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Your Translation
                      _buildSection(
                        title: 'Your Translation',
                        content: widget.userTranslation,
                        icon: Icons.person,
                        color: AppColors.primary,
                      ),

                      const SizedBox(height: 24),

                      // Suggested Translation - only show if available and different from user translation
                      if (widget.suggestedTranslation.isNotEmpty &&
                          widget.suggestedTranslation.toLowerCase() !=
                              widget.userTranslation.toLowerCase()) ...[
                        _buildSection(
                          title: 'Suggested Translation',
                          content: widget.suggestedTranslation,
                          icon: Icons.auto_fix_high,
                          color: Colors.green,
                        ),
                        const SizedBox(height: 24),
                      ],

                      // Explanation
                      _buildSection(
                        title: 'Explanation & Tips',
                        content: widget.explanation,
                        icon: Icons.lightbulb,
                        color: Colors.orange,
                      ),

                      const SizedBox(height: 32),
                    ],
                  ),
                ),
              ),

              // Action buttons
              Padding(
                padding: const EdgeInsets.all(24),
                child: Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () => Navigator.of(context).pop(),
                        style: OutlinedButton.styleFrom(
                          side: const BorderSide(color: AppColors.surface),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                        icon: const Icon(
                          Icons.bookmark_border,
                          color: AppColors.textSecondary,
                          size: 18,
                        ),
                        label: const Text(
                          'Save',
                          style: TextStyle(
                            color: AppColors.textSecondary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () => Navigator.of(context).pop(),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          foregroundColor: AppColors.background,
                          elevation: 0,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                        child: const Text(
                          'Continue',
                          style: TextStyle(fontWeight: FontWeight.w600),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        // Confetti widget - positioned at the top center with wider spread
        Positioned(
          top: 0,
          left: MediaQuery.of(context).size.width / 2 - 50, // Wider positioning
          child: ConfettiWidget(
            confettiController: _confettiController,
            blastDirection: 1.5708, // radians for straight down
            blastDirectionality:
                BlastDirectionality.explosive, // Spread in all directions
            particleDrag: 0.15,
            emissionFrequency: 0.2,
            numberOfParticles: 15, // Further reduced
            gravity: 0.15,
            shouldLoop: false,
            minBlastForce: 5, // Smaller force
            maxBlastForce: 15, // Smaller max force
            colors: const [Colors.green, Colors.blue, Colors.orange],
          ),
        ),
      ],
    );
  }

  Widget _buildSection({
    required String title,
    required String content,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: color.withOpacity(0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withOpacity(0.2), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 18),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            content,
            style: const TextStyle(
              fontSize: 15,
              color: AppColors.textPrimary,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }
}

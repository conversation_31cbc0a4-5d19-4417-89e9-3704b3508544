import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import '../models/difficulty.dart';
import '../services/translation_service.dart';

class DifficultySelectionModal {
  static void show(
    BuildContext context, {
    required String selectedDifficultyId,
    required Function(Difficulty) onDifficultySelected,
    String? title,
    String? description,
  }) {
    final TranslationService translationService = TranslationService();
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _DifficultySelectionContent(
        selectedDifficultyId: selectedDifficultyId,
        onDifficultySelected: onDifficultySelected,
        title: title ?? translationService.translate('select_difficulty'),
        description:
            description ??
            translationService.translate('select_difficulty_desc'),
      ),
    );
  }
}

class _DifficultySelectionContent extends StatelessWidget {
  final String selectedDifficultyId;
  final Function(Difficulty) onDifficultySelected;
  final String title;
  final String description;

  const _DifficultySelectionContent({
    required this.selectedDifficultyId,
    required this.onDifficultySelected,
    required this.title,
    required this.description,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 12, bottom: 8),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: AppColors.surface,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.fromLTRB(24, 16, 24, 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        title,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(
                        Icons.close,
                        color: AppColors.textSecondary,
                      ),
                      splashColor: Colors.transparent,
                      highlightColor: Colors.transparent,
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),

          // Difficulty list
          Flexible(
            child: ListView.builder(
              shrinkWrap: true,
              padding: const EdgeInsets.fromLTRB(24, 8, 24, 32),
              itemCount: Difficulties.all.length,
              itemBuilder: (context, index) {
                final difficulty = Difficulties.all[index];
                final isSelected = difficulty.id == selectedDifficultyId;

                return Container(
                  margin: const EdgeInsets.only(bottom: 12),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () {
                        onDifficultySelected(difficulty);
                        Navigator.pop(context);
                      },
                      borderRadius: BorderRadius.circular(16),
                      child: Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: isSelected
                              ? difficulty.primaryColor.withOpacity(0.05)
                              : AppColors.background,
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: isSelected
                                ? difficulty.primaryColor
                                : AppColors.surface,
                            width: isSelected ? 2 : 1,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: AppColors.surface.withOpacity(0.3),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Row(
                          children: [
                            // Difficulty icon
                            Container(
                              width: 48,
                              height: 48,
                              decoration: BoxDecoration(
                                color: difficulty.secondaryColor.withOpacity(
                                  0.5,
                                ),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Icon(
                                difficulty.icon,
                                color: difficulty.primaryColor,
                                size: 24,
                              ),
                            ),

                            const SizedBox(width: 16),

                            // Difficulty info
                            Expanded(
                              child: Text(
                                difficulty.name,
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: isSelected
                                      ? AppColors.primary
                                      : AppColors.textPrimary,
                                ),
                              ),
                            ),

                            // Selection indicator
                            if (isSelected)
                              Icon(
                                Icons.check_circle,
                                color: difficulty.primaryColor,
                                size: 24,
                              ),
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppColors {
  static const Color primary = Color(0xFF370789);
  static const Color secondary = Color.fromARGB(255, 59, 59, 59);

  static const Color background = Colors.white;
  static const Color surface = Color(0xFFF2F2F7);

  static const Color textPrimary = Color(0xFF1C1C1E);
  static const Color textSecondary = Color(0xFF8E8E93);
  static const Color textDisabled = Color(0xFF9CA3AF);

  static const Color primarySoft = Color(0xFFE0E7FF);
  static const Color card = Colors.white;

  static const Color success = Color(0xFF10B981);
  static const Color warning = Color(0xFFF59E0B);
  static const Color error = Color(0xFFEF4444);

  static const List<Color> progressGradient = [
    Color(0xFF6366F1),
    Color(0xFF818CF8),
  ];
}

class AppTheme {
  static ThemeData get lightTheme {
    return ThemeData(
      scaffoldBackgroundColor: AppColors.background,

      // Font ayarları - Inter fontu kullanıyoruz
      textTheme: GoogleFonts.interTextTheme(),

      // AppBar tema
      appBarTheme: AppBarTheme(
        backgroundColor: AppColors.background,
        elevation: 0,
        surfaceTintColor: Colors.transparent,
        iconTheme: const IconThemeData(color: AppColors.primary),
        titleTextStyle: GoogleFonts.inter(
          color: AppColors.primary,
          fontSize: 18,
          fontWeight: FontWeight.w600,
        ),
      ),

      // BottomNavigationBar tema
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: AppColors.background,
        selectedItemColor: AppColors.primary,
        unselectedItemColor: AppColors.secondary,
        type: BottomNavigationBarType.fixed,
        elevation: 0,
        selectedLabelStyle: GoogleFonts.inter(
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
        unselectedLabelStyle: GoogleFonts.inter(
          fontSize: 12,
          fontWeight: FontWeight.w400,
        ),
      ),

      // Splash renkleri (ripple effect'i kaldırmak için)
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
    );
  }
}

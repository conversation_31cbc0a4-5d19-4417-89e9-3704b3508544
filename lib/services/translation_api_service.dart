import 'api_service.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:dio/dio.dart';
import '../models/tense_category.dart';

class TranslationApiService {
  static final TranslationApiService _instance =
      TranslationApiService._internal();
  factory TranslationApiService() => _instance;
  TranslationApiService._internal();

  final ApiService _apiService = ApiService();

  // Category ID mapping
  static const Map<String, int> _categoryMap = {
    'business': 1,
    'everyday': 2,
    'academic': 3,
    'travel': 4,
    'conversational': 5,
    'technical': 6,
    'exam': 7,
  };

  // Difficulty ID mapping
  static const Map<String, int> _difficultyMap = {
    'easy': 1,
    'intermediate': 2,
    'advanced': 3,
  };

  // Get example translation text from API - now returns both text and ID
  Future<Map<String, dynamic>> getExample({
    required String language,
    required String category,
    required String difficulty,
    List<TenseCategory>? tenseCategories,
  }) async {
    try {
      // First check if API is available
      final isApiAvailable = await _checkApiConnection();

      if (!isApiAvailable) {
        throw Exception('API connection failed');
      }

      final apiVersion = dotenv.env['API_VERSION'] ?? 'v1';
      final categoryId = _categoryMap[category] ?? 2; // Default to everyday
      final difficultyId = _difficultyMap[difficulty] ?? 1; // Default to easy

      final queryParameters = <String, dynamic>{
        'language': language,
        'translateDifficulty': difficultyId,
        'translateCategory': categoryId,
      };

      // Add tense categories if provided
      if (tenseCategories != null && tenseCategories.isNotEmpty) {
        queryParameters['tenseCategories'] = tenseCategories
            .map((t) => t.id)
            .toList();
      }

      final response = await _apiService.get<Map<String, dynamic>>(
        '$apiVersion/translations',
        queryParameters: queryParameters,
      );

      if (response.statusCode == 200 && response.data != null) {
        final data = response.data!;
        return {
          'text': data['text'] ?? 'No example text available',
          'id': data['id'], // API'den gelen ID
        };
      } else {
        throw Exception('Failed to get translation example');
      }
    } catch (e) {
      print('Translation API Error: $e');
      throw Exception('Failed to get example: $e');
    }
  }

  // Check if API connection is available
  Future<bool> _checkApiConnection() async {
    try {
      // Health endpoint is at root level, not under /api/
      final baseUrl = dotenv.env['BASE_URL'];

      final dio = Dio(
        BaseOptions(
          connectTimeout: const Duration(seconds: 5),
          receiveTimeout: const Duration(seconds: 5),
          sendTimeout: const Duration(seconds: 5),
        ),
      );

      final response = await dio.get('${baseUrl}health');

      return response.statusCode == 200;
    } catch (e) {
      print('Health check failed: $e');
      return false;
    }
  }

  // Public health check method
  Future<bool> checkConnection() async {
    return await _checkApiConnection();
  }

  // Check translation and get feedback
  Future<Map<String, dynamic>?> checkTranslation({
    required String userTranslation,
    required String originalText,
    required String targetLanguage,
    required String nativeLanguage,
    String? remoteReferenceId, // New parameter for the ID from getExample
  }) async {
    try {
      // First check if API is available
      final isApiAvailable = await _checkApiConnection();

      if (!isApiAvailable) {
        throw Exception('API connection failed');
      }

      final apiVersion = dotenv.env['API_VERSION'] ?? 'v1';

      final requestData = {
        'userTranslation': userTranslation,
        'originalText': originalText,
        'targetLanguage': targetLanguage,
        'nativeLanguage': nativeLanguage,
        if (remoteReferenceId != null) 'remoteReferenceId': remoteReferenceId,
      };

      final response = await _apiService.post<Map<String, dynamic>>(
        '$apiVersion/translations',
        data: requestData,
      );

      if (response.statusCode == 201 && response.data != null) {
        return response.data;
      } else {
        throw Exception('Failed to check translation');
      }
    } catch (e) {
      print('Translation Check API Error: $e');
      throw Exception('Failed to check translation: $e');
    }
  }

  // Get available categories
  static List<String> getCategories() {
    return _categoryMap.keys.toList();
  }

  // Get available difficulties
  static List<String> getDifficulties() {
    return _difficultyMap.keys.toList();
  }

  // Get category ID by name
  static int getCategoryId(String category) {
    return _categoryMap[category] ?? 2;
  }

  // Get difficulty ID by name
  static int getDifficultyId(String difficulty) {
    return _difficultyMap[difficulty] ?? 1;
  }
}

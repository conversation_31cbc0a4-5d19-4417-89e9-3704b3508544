import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

class AuthService extends ChangeNotifier {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn(
    // Add client ID for web if needed
    serverClientId:
        '397465129541-br7ifi5tcns1qo294sdamoa99ddmcvua.apps.googleusercontent.com',
  );
  User? _user;

  User? get user => _user;
  bool get isAuthenticated => _user != null;

  AuthService() {
    // Listen to auth state changes
    _auth.authStateChanges().listen((User? user) {
      _user = user;
      notifyListeners();
    });
  }

  // Sign in with Google
  Future<UserCredential?> signInWithGoogle() async {
    try {
      // Check if Google Play Services is available
      await _googleSignIn.signOut(); // Clear any cached account

      // Trigger the authentication flow
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      if (googleUser == null) {
        // User canceled the sign-in
        return null;
      }

      // Obtain the auth details from the request
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      // Create a new credential
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      // Once signed in, return the UserCredential
      return await _auth.signInWithCredential(credential);
    } catch (e) {
      print('Google Sign-In Error Details: $e');

      if (e.toString().contains('ApiException: 10')) {
        throw 'Google Sign-In configuration error. Please make sure Google Services are properly set up.';
      } else if (e.toString().contains('sign_in_failed')) {
        throw 'Google Sign-In failed. Please try again or check your internet connection.';
      } else if (e.toString().contains('network_error')) {
        throw 'Network error. Please check your internet connection and try again.';
      } else {
        throw 'Failed to sign in with Google. Please try again.';
      }
    }
  }

  // Sign in with Apple
  Future<UserCredential?> signInWithApple() async {
    try {
      // Request credential for the currently signed in Apple account
      final appleCredential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      // Create an OAuthCredential from the credential returned by Apple
      final oauthCredential = OAuthProvider("apple.com").credential(
        idToken: appleCredential.identityToken,
        accessToken: appleCredential.authorizationCode,
      );

      // Sign in the user with Firebase
      return await _auth.signInWithCredential(oauthCredential);
    } catch (e) {
      throw 'Failed to sign in with Apple: ${e.toString()}';
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      await Future.wait([_auth.signOut(), _googleSignIn.signOut()]);
    } catch (e) {
      throw 'Failed to sign out. Please try again.';
    }
  }

  // Get current user display name
  String get displayName {
    return _user?.displayName ?? 'User';
  }

  // Get current user email
  String get email {
    return _user?.email ?? '';
  }

  // Get current user photo URL
  String? get photoURL {
    return _user?.photoURL;
  }

  // Get Firebase ID token for API authentication
  Future<String?> getIdToken({bool forceRefresh = false}) async {
    try {
      if (_user != null) {
        return await _user!.getIdToken(forceRefresh);
      }
      return null;
    } catch (e) {
      print('Error getting ID token: $e');
      return null;
    }
  }
}

import 'package:flutter/material.dart';
import '../services/api_service.dart';
import '../models/init_response.dart';
import '../models/category.dart';
import '../models/tense_category.dart';
import '../services/translation_service.dart';

class InitializationService extends ChangeNotifier {
  static final InitializationService _instance =
      InitializationService._internal();
  factory InitializationService() => _instance;
  InitializationService._internal();

  bool _isInitialized = false;
  bool _isLoading = false;
  String? _error;

  List<Category> _categories = [];
  List<TenseCategory> _tenseCategories = [];

  bool get isInitialized => _isInitialized;
  bool get isLoading => _isLoading;
  String? get error => _error;
  List<Category> get categories => _categories;
  List<TenseCategory> get tenseCategories => _tenseCategories;

  Future<void> initialize() async {
    if (_isInitialized) return;

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final apiService = ApiService();
      final response = await apiService.initializeApp();
      final initData = InitResponse.fromJson(response);

      // Update translation service with API data
      _updateTranslationService(initData.localizationResponses);

      // Update categories
      _updateCategories(initData.categories);

      // Update tense categories
      _updateTenseCategories(initData.tenseCategories);

      _isInitialized = true;
      _error = null;
    } catch (e) {
      _error = e.toString();
      print('Initialization error: $e');
    }

    _isLoading = false;
    notifyListeners();
  }

  void _updateTranslationService(List<LocalizationResponse> localizations) {
    final translationService = TranslationService();

    // Convert API localization data to the format expected by TranslationService
    final Map<String, Map<String, String>> translations = {};

    for (final localization in localizations) {
      translations[localization.languageCode] = localization.data;
    }

    // Update the translation service with API data
    translationService.updateTranslations(translations);
  }

  void _updateCategories(List<CategoryData> categoryData) {
    _categories = categoryData.map((data) {
      return Category(
        id: data.id,
        nameKey: data.nameKey,
        icon: _getIconForCategory(data.id),
        primaryColor: const Color(0xFF370789),
        secondaryColor: const Color(0xFFB6FF71),
      );
    }).toList();
  }

  void _updateTenseCategories(List<TenseCategoryData> tenseData) {
    _tenseCategories = tenseData.map((data) {
      return TenseCategory.fromApiData(data.id, data.nameKey);
    }).toList();
  }

  IconData _getIconForCategory(String categoryId) {
    // Import iconsax at the top of the file
    switch (categoryId) {
      case 'everyday':
        return const IconData(0xe3af, fontFamily: 'Iconsax'); // home_2
      case 'business':
        return const IconData(0xe191, fontFamily: 'Iconsax'); // briefcase
      case 'travel':
        return const IconData(0xe0b8, fontFamily: 'Iconsax'); // airplane
      case 'academic':
        return const IconData(0xe14b, fontFamily: 'Iconsax'); // book_1
      default:
        return const IconData(0xe3af, fontFamily: 'Iconsax'); // default home_2
    }
  }
}

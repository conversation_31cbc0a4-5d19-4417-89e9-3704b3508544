import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/material.dart';

class TranslationService extends ChangeNotifier {
  static const String _nativeLanguageKey = 'native_language';
  static const String _targetLanguageKey = 'target_language';

  // Singleton pattern
  static final TranslationService _instance = TranslationService._internal();
  factory TranslationService() => _instance;
  TranslationService._internal();

  // Current languages
  String _nativeLanguageCode = 'turkish-tr';
  String _targetLanguageCode = 'english-en';

  String get nativeLanguageCode => _nativeLanguageCode;
  String get targetLanguageCode => _targetLanguageCode;

  // Load language preferences from local storage
  Future<void> loadLanguagePreferences() async {
    final prefs = await SharedPreferences.getInstance();
    _nativeLanguageCode = prefs.getString(_nativeLanguageKey) ?? 'turkish-tr';
    _targetLanguageCode = prefs.getString(_targetLanguageKey) ?? 'english-en';
  }

  // Save language preferences to local storage
  Future<void> saveLanguagePreferences(
    String nativeCode,
    String targetCode,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_nativeLanguageKey, nativeCode);
    await prefs.setString(_targetLanguageKey, targetCode);
    _nativeLanguageCode = nativeCode;
    _targetLanguageCode = targetCode;
    notifyListeners(); // Notify all listeners about the language change
  }

  // Update translations from API
  void updateTranslations(Map<String, Map<String, String>> apiTranslations) {
    _translations.clear();
    _translations.addAll(apiTranslations);
    notifyListeners();
  }

  // Translation map for different languages - will be updated from API
  static final Map<String, Map<String, String>> _translations = {
    // Fallback translations in case API fails
    'turkish-tr': {
      'app_title': 'LingoFlow',
      'loading': 'Yükleniyor...',
      'error': 'Hata oluştu',
      'try_again': 'Tekrar Dene',

      // Navigation
      'home': 'Ana Sayfa',
      'progress': 'İlerleme',
      'profile': 'Profil',

      // Home Screen
      'translation_practice': 'Çeviri Pratiği',
      'translation_subtitle':
          'Anadilinizdeki metni yazın ve hedef dile çevirisini ekleyin.',
      'category': 'Kategori',
      'native_language_label': 'Anadil (Türkçe)',
      'native_language_hint': 'Türkçe metni buraya yazın...',
      'target_language_label': 'Hedef Dil (İngilizce)',
      'target_language_hint': 'İngilizce çevirisini buraya yazın...',
      'check_button': 'Kontrol Et',
      'translation_checked': 'Çeviri kontrol edildi! Modal açılacak...',

      // Difficulty levels
      'easy': 'Kolay',
      'intermediate': 'Orta',
      'advanced': 'İleri',

      // Profile Screen
      'welcome_back': 'Hoşgeldin Süleyman',
      'keep_practicing':
          'Becerilerinizi geliştirmek için pratik yapmaya devam edin',
      'language_settings': 'Dil Ayarları',
      'native_language': 'Anadil',
      'native_language_desc':
          'Ana diliniz. Pratik talimatları ve örnekler bu dilde gösterilecektir.',
      'learning_language': 'Öğrenilen Dil',
      'learning_language_desc':
          'Öğrenmek istediğiniz dil. Yazma alıştırmaları bu dilde olacaktır.',
      'select_native_language': 'Anadil Seçin',
      'select_native_language_desc':
          'Talimatlar ve örnekler için ana dilinizi seçin',
      'select_learning_language': 'Öğrenilen Dil Seçin',
      'select_learning_language_desc':
          'Pratik yapmak ve geliştirmek istediğiniz dili seçin',
      'account': 'Hesap',
      'my_account': 'Hesabım',
      'my_account_desc': 'Kişisel bilgiler ve ayarlar',
      'subscription': 'Abonelik',
      'my_subscription': 'Aboneliğim',
      'my_subscription_desc': 'Abonelik planınızı yönetin',
      'options': 'Seçenekler',
      'settings': 'Ayarlar',
      'settings_desc': 'Uygulama tercihleri ve yapılandırmaları',

      // Progress Screen
      'progress_screen': 'İlerleme Ekranı',
      'stats': 'İstatistikler',
      'your_progress': 'İlerlemeniz',
      'no_stats_yet': 'Henüz istatistik yok',
      'start_practicing_to_see_stats':
          'İstatistikleri görmek için pratik yapmaya başlayın',
      'total_examples': 'Toplam Örnek',
      'completed': 'Tamamlanan',
      'average_score': 'Ortalama Puan',
      'languages': 'Diller',
      'completion_progress': 'Tamamlama İlerlemesi',
      'tense_performance': 'Zaman Performansı',
      'present': 'Şimdiki Zaman',
      'past': 'Geçmiş Zaman',
      'future': 'Gelecek Zaman',
      'perfect': 'Mükemmel Zaman',
      'present_short': 'Şimdiki',
      'past_short': 'Geçmiş',
      'future_short': 'Gelecek',
      'perfect_short': 'Mükemmel',

      // Categories
      'everyday': 'Günlük',
      'business': 'İş',
      'travel': 'Seyahat',
      'academic': 'Akademik',

      // Selection Modals
      'select_category': 'Kategori Seçin',
      'select_category_desc': 'Pratik için bir kategori seçin',
      'select_difficulty': 'Zorluk Seçin',
      'select_difficulty_desc': 'Çeviri pratiği için zorluk seviyesini seçin',

      // Tense Selection Modal
      'select_tenses': 'Zaman Kategorileri Seç',
      'select_tenses_desc': 'Pratik için zaman kategorilerini seçin',
      'clear_all': 'Tümünü Temizle',
      'cancel': 'İptal',
      'apply': 'Uygula',

      // Language Swap
      'quick_swap': 'Hızlı Değiştir',
      'swap_languages_desc': 'Anadil ve hedef dili tek dokunuşla değiştirin',
      'native': 'Anadil',
      'learning': 'Öğrenilen',
      'languages_swapped': 'Diller başarıyla değiştirildi!',

      // Error and Action Messages
      'performance_details': 'Performans Detayları',
      'language_select': 'Dil Seç',
      'connection_error': 'Bağlantı Hatası',
      'connection_error_msg':
          'Sunucuya bağlanılamıyor. İnternet bağlantınızı kontrol edin ve tekrar deneyin.',
      'failed_translation_check':
          'Çeviri kontrol edilemedi. Lütfen tekrar deneyin.',
      'start_practicing': 'Pratik Yapmaya Başla',
      'analysis_requirement':
          'Analizlerimizi yapabilmek için en az birkaç çeviri örneği tamamlamanız gerekmektedir.',

      // Profile and Authentication
      'sign_out': 'Çıkış Yap',

      // Translation Result Modal
      'needs_improvement': 'Geliştirilmeli',
      'translation_result': 'Çeviri Sonucu',
      'your_translation': 'Çeviriniz',
      'suggested_translation': 'Önerilen Çeviri',
    },

    'english-en': {
      'app_title': 'LingoFlow',
      'loading': 'Loading...',
      'error': 'An error occurred',
      'try_again': 'Try Again',

      // Navigation
      'home': 'Home',
      'progress': 'Progress',
      'profile': 'Profile',

      // Home Screen
      'translation_practice': 'Translation Practice',
      'translation_subtitle':
          'Write text in your native language and add its translation to the target language.',
      'category': 'Category',
      'native_language_label': 'Native Language (English)',
      'native_language_hint': 'Write English text here...',
      'target_language_label': 'Target Language (Turkish)',
      'target_language_hint': 'Write Turkish translation here...',
      'check_button': 'Check',
      'translation_checked': 'Translation checked! Modal will open...',

      // Difficulty levels
      'easy': 'Easy',
      'intermediate': 'Intermediate',
      'advanced': 'Advanced',

      // Profile Screen
      'welcome_back': 'Welcome back Süleyman!',
      'keep_practicing': 'Keep practicing to improve your skills',
      'language_settings': 'Language Settings',
      'native_language': 'Native Language',
      'native_language_desc':
          'Your mother tongue. Practice instructions and examples will be shown in this language.',
      'learning_language': 'Learning Language',
      'learning_language_desc':
          'The language you want to learn. Writing exercises will be in this language.',
      'select_native_language': 'Select Native Language',
      'select_native_language_desc':
          'Choose your mother tongue for instructions and examples',
      'select_learning_language': 'Select Learning Language',
      'select_learning_language_desc':
          'Choose the language you want to practice and improve',
      'account': 'Account',
      'my_account': 'My Account',
      'my_account_desc': 'Personal information and settings',
      'subscription': 'Subscription',
      'my_subscription': 'My Subscription',
      'my_subscription_desc': 'Manage your subscription plan',
      'options': 'Options',
      'settings': 'Settings',
      'settings_desc': 'App preferences and configurations',

      // Progress Screen
      'progress_screen': 'Progress Screen',
      'stats': 'Statistics',
      'your_progress': 'Your Progress',
      'no_stats_yet': 'No statistics yet',
      'start_practicing_to_see_stats': 'Start practicing to see statistics',
      'total_examples': 'Total Examples',
      'completed': 'Completed',
      'average_score': 'Average Score',
      'languages': 'Languages',
      'completion_progress': 'Completion Progress',
      'tense_performance': 'Tense Performance',
      'present': 'Present Tense',
      'past': 'Past Tense',
      'future': 'Future Tense',
      'perfect': 'Perfect Tense',
      'present_short': 'Present',
      'past_short': 'Past',
      'future_short': 'Future',
      'perfect_short': 'Perfect',

      // Categories
      'everyday': 'Daily',
      'business': 'Business',
      'travel': 'Travel',
      'academic': 'Academic',

      // Selection Modals
      'select_category': 'Select Category',
      'select_category_desc': 'Choose a category for practice',
      'select_difficulty': 'Select Difficulty',
      'select_difficulty_desc':
          'Choose the difficulty level for translation practice',

      // Tense Selection Modal
      'select_tenses': 'Select Tenses',
      'select_tenses_desc': 'Choose tense categories for your practice',
      'clear_all': 'Clear All',
      'cancel': 'Cancel',
      'apply': 'Apply',

      // Language Swap
      'quick_swap': 'Quick Swap',
      'swap_languages_desc': 'Switch native and target languages with one tap',
      'native': 'Native',
      'learning': 'Learning',
      'languages_swapped': 'Languages swapped successfully!',

      // Error and Action Messages
      'performance_details': 'Performance Details',
      'language_select': 'Select Language',
      'connection_error': 'Connection Error',
      'connection_error_msg':
          'Unable to connect to the server. Please check your internet connection and try again.',
      'failed_translation_check':
          'Failed to check translation. Please try again.',
      'start_practicing': 'Start Practicing',
      'analysis_requirement':
          'You need to complete at least a few translation examples for us to perform our analyses.',

      // Profile and Authentication
      'sign_out': 'Sign Out',

      // Translation Result Modal
      'needs_improvement': 'Needs Improvement',
      'translation_result': 'Translation Result',
      'your_translation': 'Your Translation',
      'suggested_translation': 'Suggested Translation',
    },

    'spanish-es': {
      'app_title': 'LingoFlow',
      'loading': 'Cargando...',
      'error': 'Ocurrió un error',
      'try_again': 'Intentar de Nuevo',

      // Navigation
      'home': 'Inicio',
      'progress': 'Progreso',
      'profile': 'Perfil',

      // Home Screen
      'translation_practice': 'Práctica de Traducción',
      'translation_subtitle':
          'Escribe texto en tu idioma nativo y añade su traducción al idioma objetivo.',
      'category': 'Categoría',
      'native_language_label': 'Idioma Nativo (Español)',
      'native_language_hint': 'Escribe texto en español aquí...',
      'target_language_label': 'Idioma Objetivo (Inglés)',
      'target_language_hint': 'Escribe la traducción en inglés aquí...',
      'check_button': 'Verificar',
      'translation_checked': '¡Traducción verificada! Se abrirá el modal...',

      // Difficulty levels
      'easy': 'Fácil',
      'intermediate': 'Intermedio',
      'advanced': 'Avanzado',

      // Profile Screen
      'welcome_back': '¡Bienvenido de nuevo Süleyman!',
      'keep_practicing': 'Sigue practicando para mejorar tus habilidades',
      'language_settings': 'Configuración de Idioma',
      'native_language': 'Idioma Nativo',
      'native_language_desc':
          'Tu lengua materna. Las instrucciones y ejemplos se mostrarán en este idioma.',
      'learning_language': 'Idioma de Aprendizaje',
      'learning_language_desc':
          'El idioma que quieres aprender. Los ejercicios de escritura estarán en este idioma.',
      'select_native_language': 'Seleccionar Idioma Nativo',
      'select_native_language_desc':
          'Elige tu lengua materna para instrucciones y ejemplos',
      'select_learning_language': 'Seleccionar Idioma de Aprendizaje',
      'select_learning_language_desc':
          'Elige el idioma que quieres practicar y mejorar',
      'account': 'Cuenta',
      'my_account': 'Mi Cuenta',
      'my_account_desc': 'Información personal y configuraciones',
      'subscription': 'Suscripción',
      'my_subscription': 'Mi Suscripción',
      'my_subscription_desc': 'Gestiona tu plan de suscripción',
      'options': 'Opciones',
      'settings': 'Configuraciones',
      'settings_desc': 'Preferencias de la aplicación y configuraciones',

      // Progress Screen
      'progress_screen': 'Pantalla de Progreso',
      'stats': 'Estadísticas',
      'your_progress': 'Tu Progreso',
      'no_stats_yet': 'Aún no hay estadísticas',
      'start_practicing_to_see_stats':
          'Comienza a practicar para ver estadísticas',
      'total_examples': 'Ejemplos Totales',
      'completed': 'Completado',
      'average_score': 'Puntuación Promedio',
      'languages': 'Idiomas',
      'completion_progress': 'Progreso de Finalización',
      'tense_performance': 'Rendimiento de Tiempos',
      'present': 'Tiempo Presente',
      'past': 'Tiempo Pasado',
      'future': 'Tiempo Futuro',
      'perfect': 'Tiempo Perfecto',
      'present_short': 'Presente',
      'past_short': 'Pasado',
      'future_short': 'Futuro',
      'perfect_short': 'Perfecto',

      // Categories
      'everyday': 'Diario',
      'business': 'Negocios',
      'travel': 'Viaje',
      'academic': 'Académico',

      // Selection Modals
      'select_category': 'Seleccionar Categoría',
      'select_category_desc': 'Elige una categoría para practicar',
      'select_difficulty': 'Seleccionar Dificultad',
      'select_difficulty_desc':
          'Elige el nivel de dificultad para la práctica de traducción',

      // Tense Selection Modal
      'select_tenses': 'Seleccionar Tiempos',
      'select_tenses_desc': 'Elige categorías de tiempo para tu práctica',
      'clear_all': 'Limpiar Todo',
      'cancel': 'Cancelar',
      'apply': 'Aplicar',

      // Language Swap
      'quick_swap': 'Cambio Rápido',
      'swap_languages_desc': 'Cambia el idioma nativo y objetivo con un toque',
      'native': 'Nativo',
      'learning': 'Aprendizaje',
      'languages_swapped': '¡Idiomas cambiados exitosamente!',

      // Error and Action Messages
      'performance_details': 'Detalles de Rendimiento',
      'language_select': 'Seleccionar Idioma',
      'connection_error': 'Error de Conexión',
      'connection_error_msg':
          'No se puede conectar al servidor. Verifica tu conexión a internet e intenta de nuevo.',
      'failed_translation_check':
          'No se pudo verificar la traducción. Inténtalo de nuevo.',
      'start_practicing': 'Comenzar a Practicar',
      'analysis_requirement':
          'Necesitas completar al menos algunos ejemplos de traducción para que podamos realizar nuestros análisis.',

      // Profile and Authentication
      'sign_out': 'Cerrar Sesión',

      // Translation Result Modal
      'needs_improvement': 'Necesita Mejora',
      'translation_result': 'Resultado de Traducción',
      'your_translation': 'Tu Traducción',
      'suggested_translation': 'Traducción Sugerida',
    },
  };

  // Get translated text based on current native language
  String translate(String key) {
    final languageMap = _translations[_nativeLanguageCode];
    return languageMap?[key] ?? key;
  }

  // Get all available languages for translation
  List<String> get availableLanguages => _translations.keys.toList();
}
